'use client';

import React, { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { Plus, Package, Trash2 } from 'lucide-react';
import { ProposalFormData, ProposalProduct } from '@/types/component/proposals/TypeProposal';
import { constants } from 'buffer';

interface ComponentProposalProductsProps {
    onAddProduct: () => void;
    onRemoveProduct: (productId: string) => void;
    onUpdateProduct: (productId: string, updatedProduct: Partial<ProposalProduct>) => void;
}

const ComponentProposalProducts: React.FC<ComponentProposalProductsProps> = ({
    onAddProduct,
    onRemoveProduct,
    onUpdateProduct
}) => {
    const { watch } = useFormContext<ProposalFormData>();

    const products = watch('products');
    const subtotal = watch('subtotal');
    const totalDiscount = watch('totalDiscount');
    const total = watch('total');
    const totalTax = watch('totalTax');



    const formatCurrency = (value: number, currency: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
        }).format(value);
    };

    const handleTaxPercentageChange = (productId: string, percentage: number) => {
        const product = products.find(p => p.id === productId);

        if (product) {
            const totalSellingPrice = (product.sellingPrice.value || 0) * product.productUnit.unit;
            const taxAmount = (totalSellingPrice * percentage) / 100;
            onUpdateProduct(productId, { tax: { percentage: percentage || 0, value: taxAmount, currency: product.listPrice.currency } });
        }
    }

    const handleQuantityChange = (productId: string, quantity: number) => {
        const product = products.find(p => p.id === productId);
        if (product) {
            const sellingPrice = product.sellingPrice.value || 0;
            const totalSellingPrice = sellingPrice * quantity;

            const taxPercentage = product.tax?.percentage || 0;
            const taxAmount = (totalSellingPrice * taxPercentage) / 100;

            onUpdateProduct(productId, { productUnit: { unit: quantity, unitType: product.productUnit.unitType } });
            onUpdateProduct(productId, { tax: { percentage: taxPercentage, value: taxAmount, currency: product.sellingPrice.currency } });
        }
    };

    const handleUnitTypeChange = (productId: string, unitType: string) => {
        const product = products.find(p => p.id === productId);
        if (product) {
            onUpdateProduct(productId, { productUnit: { unit: product.productUnit.unit, unitType } });
        }
    };

    const handleDiscountPercentageChange = (productId: string, percentage: number) => {
        const product = products.find(p => p.id === productId);
        if (product) {
            const discountAmount = (product.listPrice.value * percentage) / 100;
            const sellingPrice = product.listPrice.value - discountAmount;

            const totalSellingPrice = sellingPrice * product.productUnit.unit;
            const taxAmount = (totalSellingPrice * (product.tax?.percentage || 0)) / 100;

            onUpdateProduct(productId, { discount: { percentage, value: discountAmount, currency: product.listPrice.currency } });
            onUpdateProduct(productId, { sellingPrice: { value: sellingPrice, currency: product.listPrice.currency } });
            onUpdateProduct(productId, { tax: { percentage: product.tax?.percentage || 0, value: taxAmount, currency: product.listPrice.currency } });
        }
    };

    return (
        <div>
            <div className="flex flex-row justify-between mb-4">
                <div>
                    <h2 className="text-xl font-bold text-gray-900">Products & Pricing Details</h2>
                    <p className="text-sm text-gray-600 mt-1">
                        Product details and pricing information of the proposal
                    </p>
                </div>
                <button
                    type="button"
                    onClick={onAddProduct}
                    className="flex items-center h-full py-2 gap-1 px-1 text-secondary hover:underline rounded-md transition-colors"
                >
                    <Plus className="h-4 w-4" />
                    Add Product
                </button>
            </div>
            <div className="flex flex-col gap-3 ">
                <div className="flex flex-col gap-3">
                    {products.length === 0 ? (
                        <div className="text-center bg-white py-8 rounded-lg border text-gray-500 border-gray-200">
                            <Package className="h-12 w-12 mx-auto mb-3 text-secondary" />
                            <p>No Products added yet</p>
                            <p className="text-sm">Click "Add Product" to get started</p>
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {products.map((product) => (
                                <div key={product.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
                                    <div className="flex items-start justify-between gap-3">
                                        <div className="flex flex-col gap-3">
                                            <div className="flex items-center gap-3 mb-2">
                                                <h4 className="font-medium text-gray-900">{product.name}</h4>
                                                <div className="flex gap-1">
                                                    {product.dimensions.map((dim, index) => (
                                                        <span
                                                            key={index}
                                                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                                        >
                                                            {dim.key}: {dim.value}
                                                        </span>
                                                    ))}
                                                </div>
                                            </div>

                                            <div className="grid grid-cols-2 md:grid-cols-6 gap-6 text-sm">
                                                <div className="flex flex-col w-32">
                                                    <span className="text-gray-600">List Price</span>
                                                    <div className="font-medium mt-1 truncate">
                                                        {formatCurrency(product.listPrice.value, product.listPrice.currency)} / {product.productUnit.unitType}
                                                    </div>
                                                </div>
                                                <div className="flex flex-col w-40">
                                                    <span className="text-gray-600">Discount (%) / {product.productUnit.unitType}</span>
                                                    <div className='flex flex-row gap-2 items-center mt-1'>
                                                        <div>
                                                            <input type="number"
                                                                min="0"
                                                                value={product.discount?.percentage || 0}
                                                                onChange={(e) => handleDiscountPercentageChange(product.id, parseFloat(e.target.value) || 0)}
                                                                className="w-16 border-b border-gray-300 text-xs"
                                                                aria-label='%'>
                                                            </input>
                                                        </div>
                                                    </div>
                                                </div>
                                                {/* <div className="flex flex-col w-32">
                                                    <span className="text-gray-600">Selling Price</span>
                                                    <div className="font-bold mt-1 truncate">
                                                        {formatCurrency(product.sellingPrice.value, product.sellingPrice.currency)}
                                                    </div>
                                                </div> */}
                                                <div className="flex flex-col w-20">
                                                    <span className="text-gray-600">Quantity</span>
                                                    <input
                                                        type="number"
                                                        min="1"
                                                        value={product.productUnit.unit}
                                                        onChange={(e) => handleQuantityChange(product.id, parseInt(e.target.value) || 1)}
                                                        className="w-20 mt-1 border-b border-gray-300 text-xs"
                                                    />
                                                </div>

                                                <div className="flex flex-col w-32">
                                                    <span className="text-gray-600">Total Discount</span>
                                                    <div className="font-bold text-gray-900 mt-1 truncate">
                                                        {formatCurrency((product.listPrice.value * product.productUnit.unit * (product.discount?.percentage || 0.00)) / 100, product.sellingPrice.currency)}
                                                    </div>
                                                </div>

                                                <div className="flex flex-col w-32">
                                                    <span className="text-gray-600">Total Selling Price</span>
                                                    <div className="font-bold text-gray-900 mt-1 truncate">
                                                        {formatCurrency(product.sellingPrice.value * product.productUnit.unit, product.sellingPrice.currency)}
                                                    </div>
                                                </div>
                                                <div className="flex flex-col w-40">
                                                    <span className="text-gray-600">Total Tax (%)</span>
                                                    <div className='flex flex-row gap-2 items-center mt-1'>
                                                        <div>
                                                            <input type="number"
                                                                min="0"
                                                                value={product.tax?.percentage || 0}
                                                                onChange={(e) => handleTaxPercentageChange(product.id, parseFloat(e.target.value) || 0)}
                                                                className="w-16 border-b border-gray-300 text-xs"
                                                                aria-label='%'>
                                                            </input>
                                                        </div>
                                                        <div className="truncate">
                                                            <span className="text-primary font-bold text-md">
                                                                {product.tax ?
                                                                    formatCurrency(product.tax.value, product.tax.currency) :
                                                                    '$0.00'
                                                                }
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-2 ml-4">
                                            <button
                                                type="button"
                                                onClick={() => onRemoveProduct(product.id)}
                                                className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                                title="Remove product"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Totals Summary */}
                {products.length > 0 && (
                    <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Proposal Summary</h3>
                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Subtotal:</span>
                                <span className="font-medium">
                                    {formatCurrency(subtotal.value, subtotal.currency)}
                                </span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Total Discount:</span>
                                <span className="font-medium text-green-600">
                                    -{formatCurrency(totalDiscount.value, totalDiscount.currency)}
                                </span>
                            </div>
                            <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                                <span className="text-md font-medium text-gray-900">Total Tax:</span>
                                <span className="text-md font-medium text-gray-900">
                                    {formatCurrency(totalTax.value, totalTax.currency)}
                                </span>
                            </div>
                            <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                                <span className="text-lg font-bold text-gray-900">Total (incl. tax):</span>
                                <span className="text-lg font-bold text-primary">
                                    {formatCurrency(total.value, total.currency)}
                                </span>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ComponentProposalProducts;
