'use client';

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { FileText } from 'lucide-react';
import { ProposalFormData } from '@/types/component/proposals/TypeProposal';

const ComponentProposalAdditionalDetails: React.FC = () => {
    const { register, watch, setValue, formState: { errors } } = useFormContext<ProposalFormData>();

    const notes = watch('notes') || [];
    const currentNote = notes.length > 0 ? notes[0]?.content || '' : '';

    const handleNotesChange = (content: string) => {
        const updatedNotes = [{
            content,
            tags: [] // For future expansion
        }];
        setValue('notes', updatedNotes);
    };

    return (
        <div>
            <div className="mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-2">Additional Details</h2>
                <p className="text-gray-600">Notes and additional information for the proposal</p>
            </div>

            <div className="space-y-6">
                {/* Notes Section */}
                <div className="bg-white border border-gray-200 p-4 rounded-lg">
                    <label className="flex items-center gap-2 mb-3 ml-2">
                        <FileText className="h-4 w-4 text-secondary" />
                        <span>Notes</span>
                    </label>

                    <div className="space-y-3">
                        <textarea
                            value={currentNote}
                            onChange={(e) => handleNotesChange(e.target.value)}
                            placeholder="Add any additional notes or comments for this proposal..."
                            className="w-full px-3 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-vertical min-h-[120px]"
                            rows={5}
                        />

                        {/* Future expansion placeholder */}
                        <div className="text-sm text-gray-500">
                            <p>Future features: Document uploads and custom tags will be available here.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ComponentProposalAdditionalDetails;
