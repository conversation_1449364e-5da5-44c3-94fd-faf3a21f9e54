import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { FormProvider, useForm } from 'react-hook-form';
import ComponentProposalAdditionalDetails from '../ComponentProposalAdditionalDetails';
import { ProposalFormData } from '@/types/component/proposals/TypeProposal';
import { Currency, QuoteStatus } from '@/lib/graphql/types/generated/graphql';

// Test wrapper component that provides form context
const TestWrapper: React.FC<{ children: React.ReactNode; defaultValues?: Partial<ProposalFormData> }> = ({ 
    children, 
    defaultValues = {} 
}) => {
    const methods = useForm<ProposalFormData>({
        defaultValues: {
            customer: null,
            companyBillingAddress: '',
            customerBillingAddress: '',
            quoteDetails: {
                description: '',
                dateOfQuoteSending: '',
                expiryDate: ''
            },
            accountAssignment: {
                salesExecutive: '',
                customerSupportManager: ''
            },
            products: [],
            subtotal: { value: 0, currency: Currency.Usd },
            totalDiscount: { value: 0, currency: Currency.Usd },
            total: { value: 0, currency: Currency.Usd },
            currency: Currency.Usd,
            status: QuoteStatus.Created,
            createdDate: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            files: [],
            notes: [],
            ...defaultValues
        }
    });

    return (
        <FormProvider {...methods}>
            {children}
        </FormProvider>
    );
};

describe('ComponentProposalAdditionalDetails', () => {
    it('renders the component with correct title and description', () => {
        render(
            <TestWrapper>
                <ComponentProposalAdditionalDetails />
            </TestWrapper>
        );

        expect(screen.getByText('Additional Details')).toBeInTheDocument();
        expect(screen.getByText('Notes and additional information for the proposal')).toBeInTheDocument();
        expect(screen.getByText('Notes')).toBeInTheDocument();
    });

    it('renders textarea with correct placeholder', () => {
        render(
            <TestWrapper>
                <ComponentProposalAdditionalDetails />
            </TestWrapper>
        );

        const textarea = screen.getByPlaceholderText('Add any additional notes or comments for this proposal...');
        expect(textarea).toBeInTheDocument();
        expect(textarea).toHaveAttribute('rows', '5');
    });

    it('handles notes input correctly', () => {
        render(
            <TestWrapper>
                <ComponentProposalAdditionalDetails />
            </TestWrapper>
        );

        const textarea = screen.getByPlaceholderText('Add any additional notes or comments for this proposal...');
        
        fireEvent.change(textarea, { target: { value: 'Test note content' } });
        
        expect(textarea).toHaveValue('Test note content');
    });

    it('displays existing notes when provided', () => {
        const defaultValues = {
            notes: [{
                id: '1',
                content: 'Existing note content',
                tags: []
            }]
        };

        render(
            <TestWrapper defaultValues={defaultValues}>
                <ComponentProposalAdditionalDetails />
            </TestWrapper>
        );

        const textarea = screen.getByPlaceholderText('Add any additional notes or comments for this proposal...');
        expect(textarea).toHaveValue('Existing note content');
    });

    it('shows future features placeholder text', () => {
        render(
            <TestWrapper>
                <ComponentProposalAdditionalDetails />
            </TestWrapper>
        );

        expect(screen.getByText('Future features: Document uploads and custom tags will be available here.')).toBeInTheDocument();
    });
});
