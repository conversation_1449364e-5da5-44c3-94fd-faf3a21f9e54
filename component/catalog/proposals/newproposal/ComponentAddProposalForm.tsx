'use client';

import React, { useState, useEffect } from 'react';
import { ArrowLeft, Download, Send } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm, FormProvider } from 'react-hook-form';
import ComponentLoading from '@/component/common/ComponentLoading';
import ComponentNote from '@/component/common/ComponentNote';
import { ProposalFormData, ProposalProduct } from '@/types/component/proposals/TypeProposal';
import { Currency, QuoteStatus, QuoteUpsertInput, QuoteProductCreateInput, ChargePolicy, DiscountType, DiscountLevel, CustomerType, CustomerStatus, Dimension, QuoteProduct, FileType } from '@/lib/graphql/types/generated/graphql';
import { useProposalProductUpsertMutation, useGetAllProposalsWithProductsQuery } from '@/lib/graphql/types/generated/hooks';
import { getUserFriendlyErrorMessage, categorizeError } from '@/lib/graphql/utils/errorHandling';
import { ApolloError } from '@apollo/client';
import ComponentProposalBasicDetails from './ComponentProposalBasicDetails';
import ComponentProposalProducts from './ComponentProposalProductDetails';
import ComponentProductSelectionModal from './ComponentProductSelectionModal';
import ComponentProposalAdditionalDetails from './ComponentProposalAdditionalDetails';



const ComponentAddProposalForm: React.FC = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const mode = searchParams.get('mode') || 'new';
    const proposalId = searchParams.get('id');
    const customerId = searchParams.get('customerId');

    const [isLoading, setIsLoading] = useState(false);
    const isEditMode = mode === 'edit';
    const [formNote, setFormNote] = useState<React.ReactNode | null>(null);
    const [isProductModalOpen, setIsProductModalOpen] = useState(false);
    const [isCurrencySelectionEnabled, setCurrencySelectionEnabled] = useState(true);

    // Apollo mutation hook for proposal creation
    const [proposalProductUpsert] = useProposalProductUpsertMutation();


    // Apollo query hook for fetching proposal data in edit mode
    const queryVariables = { quoteId: proposalId || '' };
    const shouldSkip = !isEditMode || !proposalId;

    const { data: proposalData, loading: proposalLoading, error: proposalError, refetch: refetchProposalData } = useGetAllProposalsWithProductsQuery({
        variables: queryVariables,
        skip: shouldSkip,
        fetchPolicy: 'cache-and-network'
    });




    const methods = useForm<ProposalFormData>({
        defaultValues: {
            customer: null,
            companyBillingAddress: '',
            customerBillingAddress: '',
            quoteDetails: {
                description: '',
                dateOfQuoteSending: '',
                expiryDate: ''
            },
            accountAssignment: {
                salesExecutive: '',
                customerSupportManager: ''
            },
            products: [],
            subtotal: {
                value: 0,
                currency: Currency.Usd
            },
            totalDiscount: {
                value: 0,
                currency: Currency.Usd
            },
            total: {
                value: 0,
                currency: Currency.Usd
            },
            currency: Currency.Usd,
            status: QuoteStatus.Created,
            createdDate: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            files: [],
            notes: []
        }
    });

    const { watch, setValue, getValues } = methods;
    const watchedProducts = watch('products');
    const quoteCurrency = watch('currency')
    const watchedFiles = watch('files');

    const {
        formState: { isDirty, isValid, errors }
    } = useForm({
        mode: "onChange" // Important: validates on change, updating isValid
    });

    // Calculate totals when products change
    useEffect(() => {
        const products = getValues('products');
        const subtotal = products.reduce((sum, product) => sum + (product.listPrice.value * product.productUnit.unit), 0);
        const totalDiscount = products.reduce((sum, product) => sum + ((product.discount?.value || 0) * product.productUnit.unit), 0);
        const totalTax = products.reduce((sum, product) => sum + (product.tax?.value || 0), 0);
        const total = subtotal - totalDiscount + totalTax;
        setValue('subtotal', { value: subtotal, currency: quoteCurrency });
        setValue('totalDiscount', { value: totalDiscount, currency: quoteCurrency });
        setValue('total', { value: total, currency: quoteCurrency });
        setValue('totalTax', { value: totalTax, currency: quoteCurrency });

        if (products.length != 0) {
            setCurrencySelectionEnabled(false);
        } else {
            setCurrencySelectionEnabled(true);
        }

    }, [watchedProducts, setValue, getValues, quoteCurrency]);

    // Load existing proposal data when in edit mode
    useEffect(() => {
        if (isEditMode && !proposalLoading && proposalData?.quotesGet && proposalData.quotesGet.length > 0) {
            const proposal = proposalData.quotesGet[0]; // Get the first (and should be only) proposal
            if (proposal) {
                // Transform GraphQL data to form format
                const customerName = proposal.customer?.__typename === 'CustomerBusiness'
                    ? proposal.customer.basicDetails?.legalName || ''
                    : proposal.customer?.__typename === 'CustomerIndividual'
                        ? proposal.customer.basicDetails?.contactDetails?.name || ''
                        : '';

                const customerType = proposal.customer?.__typename === 'CustomerBusiness'
                    ? CustomerType.Business
                    : CustomerType.Individual;

                // Transform products from GraphQL format to ProposalProduct format
                const transformedProducts: ProposalProduct[] = proposal.products?.map((product: any) => {
                    return {
                        id: product.id,
                        name: product.name || '',
                        productCode: product.productCode || '',
                        description: product.description || '',
                        dimensions: product.dimensions?.map((d: Dimension) => ({
                            key: d.key,
                            value: d.value
                        })) || [],
                        listPrice: {
                            value: product.pricing?.listPrice?.value || 0,
                            currency: product.pricing?.listPrice?.currency || Currency.Usd
                        },
                        discount: product.pricing?.discount && product.pricing.discount.length > 0 ? {
                            percentage: product.pricing.discount[0]?.discountValue?.percentage || 0,
                            value: product.pricing.discount[0]?.discountValue?.value?.value || 0,
                            currency: product.pricing.discount[0]?.discountValue?.value?.currency || Currency.Usd
                        } : undefined,
                        sellingPrice: {
                            value: product.pricing?.sellingPrice?.value || 0,
                            currency: product.pricing?.sellingPrice?.currency || Currency.Usd
                        },
                        productUnit: {
                            unit: product.pricing?.unit?.unit || 1,
                            unitType: product.pricing?.unit?.unitType || 'piece'
                        },
                        tax: {
                            percentage: product.pricing?.tax ? product.pricing.tax[0]?.percentage || 0 : 0,
                            value: product.pricing?.tax ? product.pricing.tax[0]?.amount?.value || 0 : 0,
                            currency: product.pricing?.tax ? product.pricing.tax[0]?.amount?.currency || Currency.Usd : Currency.Usd
                        },
                        referenceProduct: {
                            type: product.productType,
                            id: product.id
                        }
                    }
                }) || [];

                methods.reset({
                    proposalId: proposal.id,
                    customer: proposal.customer ? {
                        id: proposal.customer.id,
                        name: customerName,
                        type: customerType,
                        status: CustomerStatus.Active, // Default status
                        email: '', // Not available in current schema
                        phone: '' // Not available in current schema
                    } : null,
                    companyBillingAddress: proposal.address.fromAddress || '', // Not available in current schema
                    customerBillingAddress: proposal.address.toAddress || '', // Not available in current schema

                    quoteDetails: {
                        description: proposal.description || '',
                        dateOfQuoteSending: new Date(proposal.date?.validFrom).toISOString().split('T')[0],
                        expiryDate: proposal.date?.validTill ? new Date(proposal.date.validTill).toISOString().split('T')[0] : ''
                    },
                    accountAssignment: {
                        salesExecutive: proposal.assignments && proposal.assignments.length > 0 ? proposal.assignments[0]?.salesExecutive?.id || '' : '',
                        customerSupportManager: proposal.assignments && proposal.assignments.length > 0 ? proposal.assignments[0]?.customerSuccessManger?.id || '' : ''
                    },
                    products: transformedProducts,
                    subtotal: {
                        value: proposal.quoteTotalSellingPrice?.value || 0,
                        currency: proposal.quoteTotalSellingPrice?.currency || Currency.Usd
                    },
                    totalDiscount: {
                        value: proposal.quoteTotalDiscountAmount?.value || 0,
                        currency: proposal.quoteTotalDiscountAmount?.currency || Currency.Usd
                    },
                    totalTax: {
                        value: proposal.quoteTotalTaxAmount?.value || 0,
                        currency: proposal.quoteTotalTaxAmount?.currency || Currency.Usd
                    },
                    total: {
                        value: (proposal.quoteTotalSellingPrice?.value || 0) + (proposal.quoteTotalTaxAmount?.value || 0),
                        currency: proposal.quoteTotalSellingPrice?.currency || Currency.Usd
                    },
                    currency: proposal.currency || Currency.Usd,
                    status: proposal.status || QuoteStatus.Created,
                    files: proposal.files?.map(file => ({
                        name: file.name,
                        fileType: file.fileType,
                        base64Encoded: file.base64Encoded
                    })) as Array<{
                        name: string;
                        fileType?: FileType;
                        base64Encoded: string;
                    }> || [],
                    notes: proposal.notes?.map(note => ({
                        id: note.id || undefined,
                        content: note.content,
                        tags: [...(note.tags || [])]
                    })) || []
                });
            }
        }
    }, [proposalLoading]);


    const handleProductAdd = (product: ProposalProduct) => {
        const currentProducts = getValues('products');
        setValue('products', [...currentProducts, product]);
        setIsProductModalOpen(false);
    };

    const handleProductRemove = (productId: string) => {
        const currentProducts = getValues('products');
        setValue('products', currentProducts.filter(p => p.id !== productId));
    };

    const handleProductUpdate = (productId: string, updatedProduct: Partial<ProposalProduct>) => {
        const currentProducts = getValues('products');
        setValue('products', currentProducts.map(p =>
            p.id === productId ? { ...p, ...updatedProduct } : p
        ));
    };

    if (isLoading || (isEditMode && proposalLoading)) {
        return (
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <ComponentLoading
                    message={isEditMode ? "Loading proposal..." : "Preparing form..."}
                    className="min-h-[400px]"
                />
            </div>
        );
    }

    // Handle error state for proposal loading
    if (isEditMode && proposalError) {
        return (
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <ComponentNote isError={true}>
                    Failed to load proposal data. Please try again or contact support.
                </ComponentNote>
            </div>
        );
    }


    // Transform ProposalFormData to QuoteUpsertInput format
    const transformProposalDataToQuoteInput = (data: ProposalFormData): QuoteUpsertInput => {
        if (!data.customer) {
            throw new Error('Customer is required to create a proposal');
        }

        // Transform products to QuoteProductCreateInput format
        const quoteProductsInput: QuoteProductCreateInput[] = data.products.map(product => ({
            productCode: product.productCode,
            name: product.name,
            customerId: data.customer!.id,
            description: product.description,
            dimensions: product.dimensions.map(dim => ({
                key: dim.key,
                value: dim.value
            })),
            pricing: {
                chargePolicy: ChargePolicy.Unit, // Use the available enum value
                costPrice: {
                    value: product.listPrice.value * 0.7, // TODO: Get actual cost price
                    currency: product.listPrice.currency
                },
                listPrice: {
                    value: product.listPrice.value,
                    currency: product.listPrice.currency
                },
                sellingPrice: {
                    value: product.sellingPrice.value,
                    currency: product.sellingPrice.currency
                },
                productUnit: {
                    unit: product.productUnit.unit,
                    unitType: product.productUnit.unitType
                },
                discounts: product.discount ? [{
                    discountType: DiscountType.Percentage,
                    discountLevel: DiscountLevel.Product,
                    discountValue: {
                        amount: {
                            value: product.discount.value,
                            currency: product.discount.currency
                        },
                        percentage: product.discount.percentage
                    }
                }] : [],
                tax: product.tax ? [{
                    name: 'Tax',
                    percentage: product.tax.percentage,
                    amount: {
                        value: product.tax.value,
                        currency: product.tax.currency
                    }
                }] : [],
            },
            // Add reference to master product if available
            // referenceProduct: product.referenceProduct ? {
            //     type: product.referenceProduct.type,
            //     id: product.referenceProduct.id
            // } : {
            //     type: ProductType.Master, // Default to master product type
            //     id: product.id
            // },
            documents: [], // Keep as empty array for now
            customTags: [] // Keep as empty array for now
        }));

        return {
            id: data.proposalId, // undefined for new proposals
            description: data.quoteDetails.description,
            customerId: data.customer.id,
            assignments: [{
                salesExecutive: data.accountAssignment.salesExecutive,
                customerSuccessManger: data.accountAssignment.customerSupportManager // Note: typo in schema
            }],
            dateInput: {
                validFrom: data.quoteDetails.dateOfQuoteSending,
                validTill: data.quoteDetails.expiryDate || null // Send null if empty
            },
            addressInput: {
                fromAddress: data.companyBillingAddress,
                toAddress: data.customerBillingAddress
            },
            quoteProductsInput,
            currency: data.currency,
            paymentTerms: undefined, // TODO: Backend needs to add payment terms field
            discounts: [], // Keep as empty array
            documents: [], // Keep as empty array
            customTags: [], // Keep as empty array
            notes: data.notes?.map(note => ({
                id: note.id,
                content: note.content,
                tags: note.tags
            })) || []
        };
    };

    const handleGenerate = async (data: ProposalFormData) => {
        setIsLoading(true);
        setFormNote(null);

        try {
            // Validate required fields
            if (!data.customer) {
                setFormNote(<ComponentNote isError={true}>Please select a customer before creating the proposal.</ComponentNote>);
                setIsLoading(false);
                return;
            }

            if (data.products.length === 0) {
                setFormNote(<ComponentNote isError={true}>Please add at least one product to the proposal.</ComponentNote>);
                setIsLoading(false);
                return;
            }

            // Validate that all products have required fields
            const invalidProducts = data.products.filter(product =>
                !product.name || product.productUnit.unit <= 0
            );

            if (invalidProducts.length > 0) {
                setFormNote(<ComponentNote isError={true}>
                    Some products are missing required information (name, or valid quantity).
                    Please check all products before creating the proposal.
                </ComponentNote>);
                setIsLoading(false);
                return;
            }

            // Validate that all products have a valid currency as data.currency
            if (data.products.some(product => product.listPrice.currency !== data.currency)) {
                setFormNote(<ComponentNote isError={true}>
                    Some products have a different currency than the proposal currency which is {data.currency}.
                    Please check all products before creating the proposal.
                </ComponentNote>);
                setIsLoading(false);
                return;
            }


            // Validate quote details
            if (!data.quoteDetails.dateOfQuoteSending) {
                setFormNote(<ComponentNote isError={true}>Please set the proposal date (valid from) before creating the proposal.</ComponentNote>);
                setIsLoading(false);
                return;
            }

            // Validate that expiry date is after proposal date if provided
            if (data.quoteDetails.expiryDate && data.quoteDetails.dateOfQuoteSending) {
                const proposalDate = new Date(data.quoteDetails.dateOfQuoteSending);
                const expiryDate = new Date(data.quoteDetails.expiryDate);

                if (expiryDate <= proposalDate) {
                    setFormNote(<ComponentNote isError={true}>Expiry date must be after the proposal date.</ComponentNote>);
                    setIsLoading(false);
                    return;
                }
            }

            // Transform data and call mutation
            const quoteInput = transformProposalDataToQuoteInput(data);

            await proposalProductUpsert({
                variables: {
                    input: quoteInput
                },
                onCompleted: async (mutationData) => {
                    if (mutationData.quoteUpsert) {
                        setIsLoading(false);

                        // Update form with the new proposal ID for future edits
                        methods.setValue('proposalId', mutationData.quoteUpsert.id);

                        // Refetch proposal data if in edit mode
                        if (isEditMode && proposalId) {
                            router.replace(`/catalog/proposals/new?mode=edit&id=${mutationData.quoteUpsert.id}`);
                        }

                        setFormNote(<ComponentNote>Proposal created successfully! You can generate and download the PDF.</ComponentNote>);

                    }
                },
                onError: (error) => {
                    setIsLoading(false);
                    const errorCategory = categorizeError(error as ApolloError);

                    // Provide different error messages based on error type
                    let errorMessage = getUserFriendlyErrorMessage(error as ApolloError);

                    if (errorCategory.retryable) {
                        errorMessage += ' You can try again.';
                    }

                    if (errorCategory.type === 'validation') {
                        errorMessage = 'Please check your proposal details and try again. ' + errorCategory.technicalMessage;
                    }

                    setFormNote(<ComponentNote isError={true}>{errorMessage}</ComponentNote>);
                }
            });
        } catch (error) {
            setIsLoading(false);
            setFormNote(<ComponentNote isError={true}>
                {error instanceof Error ? error.message : 'An unexpected error occurred while creating the proposal.'}
            </ComponentNote>);
        }
    };

    const downloadPdf = async () => {
        setIsLoading(true);
        setFormNote(null);

        try {
            var a = document.createElement("a");
            a.href = "data:text/html;base64," + watchedFiles?.[0]?.base64Encoded;
            a.download = watchedFiles?.[0]?.name + ".html";
            a.click();
            setIsLoading(false);
        } catch (error) {
            setIsLoading(false);
            setFormNote(<ComponentNote isError={true}>
                {error instanceof Error ? error.message : 'An unexpected error occurred while downloading the proposal.'}
            </ComponentNote>);
        }
    };

    return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative">
            {/* Loading overlay */}
            {isLoading && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                        <ComponentLoading
                            message={isEditMode ? "Updating your proposal..." : "Creating your proposal..."}
                            className="min-h-[150px]"
                        />
                        <div className="text-center text-sm text-gray-600 mt-4">
                            Please wait while we process your proposal details...
                        </div>
                    </div>
                </div>
            )}

            <div className="mb-8">
                <div className="mb-8">
                    <button
                        onClick={() => router.replace('/catalog/proposals')}
                        className="mb-6 flex items-center text-gray-600 hover:text-secondary transition-colors"
                        disabled={isLoading}
                    >
                        <ArrowLeft className="h-5 w-5 mr-2" />
                        Back
                    </button>
                </div>
            </div>


            {formNote && (
                <div className="mt-6 mb-6">
                    {formNote}
                </div>
            )}

            <FormProvider {...methods}>
                <form className="space-y-8" onSubmit={methods.handleSubmit(handleGenerate)}>
                    {/* Section 1 */}
                    <ComponentProposalBasicDetails alreadySelectedCustomer={customerId || undefined} isCurrencySelectionEnabled={isCurrencySelectionEnabled} />

                    {/* Section 2 */}
                    <ComponentProposalProducts
                        onAddProduct={() => setIsProductModalOpen(true)}
                        onRemoveProduct={handleProductRemove}
                        onUpdateProduct={handleProductUpdate}
                    />

                    {/* Section 3 */}
                    <ComponentProposalAdditionalDetails />

                    <div className="flex items-center justify-end space-x-3">
                        <button
                            type='button'
                            disabled={isLoading || watchedFiles?.length === 0}
                            onClick={() => { downloadPdf() }}
                            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <Download className="h-4 w-4" />
                            Download Proposal
                        </button>
                        <button
                            type='submit'
                            disabled={isLoading || !isValid}
                            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <Send className="h-4 w-4" />
                            {isLoading
                                ? (isEditMode ? 'Updating Proposal...' : 'Creating Proposal...')
                                : (isEditMode ? 'Update Proposal' : 'Create Proposal')
                            }
                        </button>

                    </div>

                    {/* Product Selection Modal */}
                    <ComponentProductSelectionModal
                        isOpen={isProductModalOpen}
                        onClose={() => setIsProductModalOpen(false)}
                        onProductSelect={handleProductAdd}
                        selectedProducts={getValues('products')}
                    />
                </form>
            </FormProvider>
        </div>
    );
};

export default ComponentAddProposalForm;
