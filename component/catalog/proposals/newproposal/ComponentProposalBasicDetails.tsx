'use client';

import React, { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { User, Building2, MapPin, Calendar, FileText, Users, ChevronDown, Info, Calendar1, DollarSign } from 'lucide-react';
import { ProposalFormData, ProposalCustomer } from '@/types/component/proposals/TypeProposal';
import { CompanyUserStatus, CustomerStatus, CustomerType, Currency } from '@/lib/graphql/types/generated/graphql';
import { useGetAllCustomersQuery, useGetCompanyBasicDetailsQuery, useGetCompanyUserQuery, useGetCompanyUsersQuery } from '@/lib/graphql/types/generated/hooks';
import ComponentTooltip from '@/component/common/ComponentTooltip';
import ComponentLoading from '@/component/common/ComponentLoading';
import ComponentNote from '@/component/common/ComponentNote';
import { currencyOptions } from '@/constants/ConstantsCurrency';
import { useCompanyUserVerification } from '@/lib/graphql/hooks/useCompanyUserVerification';



const ComponentProposalBasicDetails: React.FC<{ alreadySelectedCustomer?: string; isCurrencySelectionEnabled: boolean }> = ({ alreadySelectedCustomer, isCurrencySelectionEnabled }) => {
    const { register, watch, setValue, formState: { errors } } = useFormContext<ProposalFormData>();
    const proposalId = watch('proposalId');

    const { data: companyData, loading: companyLoading, error: companyError } = useGetCompanyBasicDetailsQuery({});
    const { data: companyUsersData, loading: companyUsersLoading, error: companyUsersError } = useGetCompanyUsersQuery();
    const { companyUser: currentUser, isLoading: isUserLoading } = useCompanyUserVerification();
    const { data: customersData, loading: customersLoading, error: customersError } = useGetAllCustomersQuery({
        variables: {
            status: CustomerStatus.Active, // Only fetch active customers
        },
    });

    useEffect(() => {
        // Auto-populate company billing address
        if (companyData?.getCompany?.basicDetails?.address) {
            setValue('companyBillingAddress', companyData.getCompany.basicDetails.address);
        }
    }, [companyData]);

    useEffect(() => {
        // Auto-populate sales executive
        if (currentUser) {
            setValue('accountAssignment.salesExecutive', currentUser.id);
            setValue('accountAssignment.customerSupportManager', currentUser.id);
        }
    }, [currentUser]);

    // Transform GraphQL customer data to ProposalCustomer format
    const customers: ProposalCustomer[] = customersData?.getCustomers.map(customer => {
        let name = '';
        let email = '';
        let phone = '';

        if (customer.__typename === 'CustomerBusiness') {
            name = customer.basicDetails.legalName;
            email = customer.basicDetails.contactDetails.email;
            phone = customer.basicDetails.contactDetails.phoneNo || '';
        } else if (customer.__typename === 'CustomerIndividual') {
            name = customer.basicDetails.contactDetails.name;
            email = customer.basicDetails.contactDetails.email;
            phone = customer.basicDetails.contactDetails.phoneNo || '';
        }

        return {
            id: customer.id,
            name,
            type: customer.type,
            status: customer.status,
            email,
            phone
        };
    }) || [];

    const handleCustomerChange = (customerId: string) => {
        const customer = customers.find(c => c.id === customerId);
        setValue('customer', customer || null);

        // Auto-populate customer billing address if customer is selected
        if (customer) {
            const customerBillingAddress = {
                street: '123 Customer Street',
                city: 'Customer City',
                state: 'Customer State',
                zipCode: '12345',
                country: 'USA'
            }
            setValue('customerBillingAddress', customerBillingAddress.street);
        }
    };

    // Handle loading and error states
    if (customersLoading || companyLoading || companyUsersLoading || isUserLoading) {
        return (
            <div className="mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-2">Proposal Basic Details</h2>
                <ComponentLoading message="Loading Proposal Information..." className="min-h-[200px]" />
            </div>
        );
    }

    if (customersError || companyError || companyUsersError) {
        return (
            <div className="mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-2">Proposal Basic Details</h2>
                <ComponentNote isError={true}>
                    Error loading customers: {customersError?.message || companyError?.message || companyUsersError?.message}
                </ComponentNote>
            </div>
        );
    }

    return (
        <div>
            <div className="mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-2">Proposal Basic Details</h2>
                <p className="text-gray-600">Customer details and proposal information</p>
            </div>
            <div className="">
                <div className="space-y-6">
                    {/* Customer Selection */}
                    <div className='flex flex-row gap-2 bg-white border border-gray-200 p-4 rounded-lg'>
                        <div className='bg-white w-3/4'>
                            <label className='flex items-center gap-2 mb-3 ml-2'>
                                <User className="h-4 w-4 text-secondary" />
                                <span>Customer *</span>
                            </label>
                            <div className="relative">
                                <select
                                    disabled={proposalId ? true : false}
                                    onChange={(e) => handleCustomerChange(e.target.value)}
                                    required
                                    defaultValue={watch('customer')?.id || alreadySelectedCustomer}
                                    className="w-full pl-1 pr-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white disabled:bg-gray-100 disabled:cursor-not-allowed"
                                >
                                    <option value="">Select a customer...</option>
                                    {customers.map((customer) => (
                                        <option key={customer.id} value={customer.id}>
                                            {customer.name} ({customer.type})
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                        <div className='bg-white w-1/4'>
                            <label className='flex items-center gap-2 mb-3'>
                                <DollarSign className="h-4 w-4 text-secondary" />
                                <span>Currency *</span>
                            </label>
                            <ComponentTooltip
                                content='The currency is locked once you add products to the proposal; Remove existing products to change the currency.'
                                position='top'
                                disabled={isCurrencySelectionEnabled}
                                className='w-full'
                            >
                                <select
                                    disabled={!isCurrencySelectionEnabled}
                                    {...register('currency', { required: true })}
                                    className="text-md w-full py-3  border border-gray-300 rounded-lg bg-white disabled:bg-gray-100 disabled:cursor-not-allowed"
                                >
                                    <option value="">Select a currency...</option>
                                    {currencyOptions.map(opt => (
                                        <option key={opt.value} value={opt.value} >{opt.label} {opt.value}</option>
                                    ))}
                                </select>
                            </ComponentTooltip>
                        </div>
                    </div>

                    {/* Billing Addresses Row */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4 bg-white border border-gray-200 p-4 rounded-lg">
                            <div className="flex items-center gap-2 mb-3">
                                <Building2 className="h-4 w-4 text-secondary" />
                                Your Billing Address
                                <ComponentTooltip content='This is the billing address of your company. This can be changed in the Company Settings.'>
                                    <Info className="h-4 w-4 text-primary" />
                                </ComponentTooltip>
                            </div>
                            <div className="bg-gray-100 p-4 rounded-lg min-h-[120px]">
                                <div className="space-y-2 text-sm text-gray-600">
                                    <div>{companyData?.getCompany?.basicDetails?.name}</div>
                                    <div>{companyData?.getCompany?.basicDetails?.address}</div>
                                    <div>{companyData?.getCompany?.basicDetails?.phoneNumber}</div>
                                    <div>{companyData?.getCompany?.basicDetails?.email}</div>
                                </div>
                            </div>
                        </div>

                        {/* Customer Billing Address */}
                        <div className="space-y-4 bg-white border border-gray-200 p-4 rounded-lg">
                            <div className="flex items-center gap-2 mb-3">
                                <MapPin className="h-4 w-4 text-secondary" />
                                Customer Billing Address *
                            </div>
                            <div className="space-y-3">
                                <textarea
                                    rows={6}
                                    placeholder="Address"
                                    {...register('customerBillingAddress', { required: true })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Quote Details and Account Assignment Row */}
                    <div className="grid grid-cols-1  md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            {/* <div className="border border-gray-200 p-4 rounded-lg">
                                <label className='flex items-center gap-2 mb-3'>
                                    <FileText className="h-4 w-4 text-gray-500" />
                                    <span>Proposal's Description</span>
                                </label>
                                <textarea
                                    placeholder="A proposal for website designing services"
                                    {...register('quoteDetails.description')}
                                    rows={3}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm resize-none"
                                />
                            </div> */}
                            <div className="space-y-3 bg-white border border-gray-200 p-4 rounded-lg">
                                <div>
                                    <div className='flex items-center gap-2 mb-3'>
                                        <Calendar1 className='h-4 w-4 text-secondary' />
                                        Dates
                                    </div>
                                    <div className="flex flex-col gap-1">
                                        <label className="text-xs text-gray-600">Proposal Date *</label>
                                        <input
                                            type="date"
                                            {...register('quoteDetails.dateOfQuoteSending', { required: true })}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <div className="flex flex-col gap-1">
                                        <label className="text-xs text-gray-600">Expiry date</label>
                                        <input
                                            type="date"
                                            {...register('quoteDetails.expiryDate')}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Account Assignment */}
                        <div className="space-y-4 border bg-white border-gray-200 p-4 rounded-lg">
                            <div className="flex items-center gap-2 mb-3">
                                <Users className="h-4 w-4 text-secondary" />
                                <h3 className="text-sm">Account Assignment</h3>
                            </div>
                            <div className="space-y-3">
                                <div className="flex flex-col gap-1">
                                    <label className="text-xs text-gray-600">Sales Executive</label>
                                    <select
                                        {...register('accountAssignment.salesExecutive')}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm bg-white"
                                    >
                                        {companyUsersData?.getCompany?.users?.filter(user => user?.status === CompanyUserStatus.Active)
                                            .map(user => (
                                                <option key={user?.id} value={user?.id}>
                                                    {user?.name}
                                                </option>
                                            ))}
                                    </select>
                                </div>
                                <div className="flex flex-col gap-1">
                                    <label className="text-xs text-gray-600">Customer Support Manager</label>
                                    <select
                                        {...register('accountAssignment.customerSupportManager')}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm bg-white"
                                    >
                                        {companyUsersData?.getCompany?.users?.filter(user => user?.status === CompanyUserStatus.Active)
                                            .map(user => (
                                                <option key={user?.id} value={user?.id}>
                                                    {user?.name}
                                                </option>
                                            ))}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ComponentProposalBasicDetails;
