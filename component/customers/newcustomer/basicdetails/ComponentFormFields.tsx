import React from 'react';
import { User, Mail, Phone, Users, Building2, Globe, Briefcase, Flag, Tag } from 'lucide-react';

// Reusable Input Field Component with Icon Support
export const InputField = ({ label, name, register, placeholder, type = 'text', required = false, icon, onClick = () => { } }: any) => (
    <div>
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
        <div className="relative">
            {icon && (
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                    {icon}
                </div>
            )}
            <input
                id={name}
                type={type}
                {...register(name, { required: required })}
                placeholder={placeholder}
                className={`w-full ${icon ? 'pl-10' : 'pl-4'} pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors`}
                onClick={onClick}
            />
        </div>
    </div>
);

// Reusable Select Field Component with Icon Support
export const SelectField = ({ label, name, register, children, required = false, icon }: any) => (
    <div>
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
        <div className="relative">
            {icon && (
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                    {icon}
                </div>
            )}
            <select
                id={name}
                {...register(name, { required: required })}
                className={`w-full ${icon ? 'pl-10' : 'pl-4'} pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors appearance-none bg-white`}
            >
                {children}
            </select>
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </div>
        </div>
    </div>
);

export const icons = {
    User,
    Mail,
    Phone,
    Users,
    Building2,
    Globe,
    Briefcase,
    Flag,
    Tag,
}; 