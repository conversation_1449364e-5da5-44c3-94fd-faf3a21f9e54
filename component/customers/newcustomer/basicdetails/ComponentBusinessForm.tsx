import React from 'react';
import { useFormContext } from 'react-hook-form';
import { EntitySize, CustomerStage } from '@/lib/graphql/types/generated/graphql';
import { InputField, SelectField, icons } from './ComponentFormFields';

const BusinessForm: React.FC = () => {
    const { register } = useFormContext();

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <InputField
                    label="Company Legal Name *"
                    name="companyLegalName"
                    register={register}
                    placeholder="Enter company legal name"
                    icon={<icons.Building2 className="h-5 w-5" />}
                    required
                />
            </div>
            <div>
                <InputField
                    label="Company Website"
                    name="companyWebsite"
                    register={register}
                    placeholder="https://www.company.com"
                    icon={<icons.Globe className="h-5 w-5" />}
                />
            </div>
            <div>
                <SelectField
                    label="Company Size *"
                    name="companySize"
                    register={register}
                    icon={<icons.Users className="h-5 w-5" />}
                    required
                >
                    <option value="" disabled>Select company size</option>
                    <option value={EntitySize.LessThanTen}>1-10 employees</option>
                    <option value={EntitySize.TenToHundred}>10-100 employees</option>
                    <option value={EntitySize.HundredToThousand}>100-1000 employees</option>
                    <option value={EntitySize.GreaterThanThousand}>Greater than 1000 employees</option>
                </SelectField>
            </div>
            <div>
                <SelectField
                    label="Industry *"
                    name="industry"
                    register={register}
                    icon={<icons.Briefcase className="h-5 w-5" />}
                    required
                >
                    <option value="" disabled>Select an industry</option>
                    <option value="technology">Technology</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="finance">Finance</option>
                    <option value="retail">Retail</option>
                    <option value="manufacturing">Manufacturing</option>
                    <option value="other">Other</option>
                </SelectField>
            </div>
            <div>
                <SelectField
                    label="Customer Stage *"
                    name="stage"
                    register={register}
                    icon={<icons.Flag className="h-5 w-5" />}
                    required
                >
                    <option value="" disabled>Select customer stage</option>
                    <option value={CustomerStage.Lead}>Lead</option>
                    <option value={CustomerStage.ConversionInProgress}>Conversion In Progress</option>
                    <option value={CustomerStage.Converted}>Converted</option>
                    <option value={CustomerStage.ConversionFailed}>Conversion Failed</option>
                </SelectField>
            </div>
            <div>
                <InputField
                    label="Contact Person's Name *"
                    name="contactName"
                    register={register}
                    placeholder="Enter contact person's name"
                    icon={<icons.User className="h-5 w-5" />}
                    required
                />
            </div>
            <div>
                <InputField
                    label="Contact Person's Title"
                    name="contactTitle"
                    register={register}
                    placeholder="Enter job title"
                    icon={<icons.Briefcase className="h-5 w-5" />}
                />
            </div>
            <div>
                <InputField
                    label="Contact Email *"
                    name="contactEmail"
                    type="email"
                    register={register}
                    placeholder="Enter contact email"
                    icon={<icons.Mail className="h-5 w-5" />}
                    required
                />
            </div>
            <div>
                <InputField
                    label="Contact Phone"
                    name="contactPhone"
                    type="tel"
                    register={register}
                    placeholder="Enter contact phone"
                    icon={<icons.Phone className="h-5 w-5" />}
                />
            </div>
            <div>
                <InputField
                    label="Referral Source"
                    name="referralSource"
                    register={register}
                    placeholder="How did they find you?"
                    icon={<icons.Users className="h-5 w-5" />}
                />
            </div>
            <div>
                <InputField
                    label="Tags"
                    name="plainTags"
                    register={register}
                    placeholder="Enter tags"
                    icon={<icons.Tag className="h-5 w-5" />}
                />
            </div>
        </div>
    );
};

export default BusinessForm; 