'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { Trash, MoreHorizontal, UserPlus, Search, Plus, User, Building2 } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import customerTableHeaders from '@/constants/ConstantsCustomerTableHeaders';
import PagePagination from '@/component/pagination/ComponentPagePagination';
import { CustomerWithBasicDetails } from '@/types/component/customers/TypeCustomer';
import { CustomerBusiness, CustomerIndividual } from '@/lib/graphql/types/generated/graphql';
import { useGetAllCustomersQuery } from '@/lib/graphql/types/generated/hooks';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import ComponentLoading from '@/component/common/ComponentLoading';
import CustomersControls from './ComponentCustomersControls';

// Status color mapping for Status columns
const statusColorMap: { [key: string]: string } = {
    'ACTIVE': 'bg-green-100 text-green-800',
    'SUSPENDED': 'bg-red-100 text-red-800',
    'INACTIVE': 'bg-gray-100 text-gray-800',
    'PENDING': 'bg-yellow-100 text-yellow-800',
};

// Stage color mapping for Stage columns - distinct from status colors
const stageColorMap: { [key: string]: string } = {
    'LEAD': 'bg-blue-100 text-blue-800',
    'CONVERTED': 'bg-indigo-100 text-indigo-800',
    'CONVERSION_IN_PROGRESS': 'bg-purple-100 text-purple-800',
    'PROSPECT': 'bg-emerald-100 text-emerald-800',
    'CHURNED': 'bg-orange-100 text-orange-800',
    'DORMANT': 'bg-slate-100 text-slate-800',
};

const AllCustomersTable: React.FC = () => {
    const router = useRouter();
    const { data, loading, error: queryError } = useGetAllCustomersQuery(
        {
            variables: {},
            fetchPolicy: 'cache-and-network',
        }
    );

    const [visibleHeaders, setVisibleHeaders] = useState(customerTableHeaders);
    const [searchQuery, setSearchQuery] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [customers, setCustomers] = useState<CustomerWithBasicDetails[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [paginatedCustomers, setPaginatedCustomers] = useState<CustomerWithBasicDetails[]>([]);

    const recordsPerPage = 10;

    const filteredCustomers = useMemo(() => {
        if (!searchQuery.trim()) return customers;
        const query = searchQuery.toLowerCase().trim();
        return customers.filter(customer =>
            Object.values(customer).some(value =>
                String(value).toLowerCase().includes(query)
            )
        );
    }, [searchQuery, customers]);

    const totalPages = Math.ceil(filteredCustomers.length / recordsPerPage);
    useEffect(() => {
        const paginatedCustomers = filteredCustomers.slice(
            (currentPage - 1) * recordsPerPage,
            currentPage * recordsPerPage
        );
        setPaginatedCustomers(paginatedCustomers);
    }, [filteredCustomers, currentPage]);

    // Placeholder tags for demonstration
    const getPlaceholderTags = (customerType: string, stage: string): string[] => {
        const baseTags = [];
        if (customerType === 'BUSINESS') {
            baseTags.push('Enterprise', 'B2B');
        } else {
            baseTags.push('Individual', 'B2C');
        }

        if (stage === 'CONVERTED') {
            baseTags.push('High Value');
        } else if (stage === 'LEAD') {
            baseTags.push('New Lead');
        }

        return baseTags;
    };

    function mapCustomerIndividual(customer: CustomerIndividual): CustomerWithBasicDetails {
        return {
            id: customer.id,
            name: customer.basicDetails.contactDetails.name || '',
            email: customer.basicDetails.contactDetails.email || '',
            phone: customer.basicDetails.contactDetails.phoneNo || '',
            status: customer.status,
            stage: customer.stage,
            type: customer.type,
            website: customer.basicDetails.individualWebsite || undefined,
            tags: customer.basicDetails.plainTags || [],
            accountManager: customer.assignments?.[0]?.accountManager?.name || '',
        };
    }

    function mapCustomerBusiness(customer: CustomerBusiness): CustomerWithBasicDetails {
        return {
            id: customer.id,
            name: customer.basicDetails.contactDetails.name || '',
            email: customer.basicDetails.contactDetails.email || '',
            phone: customer.basicDetails.contactDetails.phoneNo || '',
            status: customer.status,
            stage: customer.stage,
            type: customer.type,
            website: customer.basicDetails.website || undefined,
            tags: customer.basicDetails.plainTags || [],
            accountManager: customer.assignments?.[0]?.accountManager?.name || '',
        };
    }

    React.useEffect(() => {
        setCurrentPage(1);
        if (loading) {
            setIsLoading(true);
            return;
        }

        if (queryError) {
            setError(getUserFriendlyErrorMessage(queryError));
            setIsLoading(false);
            return;
        }

        if (data) {
            setCustomers(data?.getCustomers.map(customer => {
                if (customer.__typename === 'CustomerIndividual') {
                    return mapCustomerIndividual(customer as CustomerIndividual);
                }
                if (customer.__typename === 'CustomerBusiness') {
                    return mapCustomerBusiness(customer as CustomerBusiness);
                }
                return null;
            }) as CustomerWithBasicDetails[]);
            setIsLoading(false);
        }

    }, [searchQuery, data, queryError, loading]);

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const handleRowClick = (customerId: string) => {
        router.replace(`/customers/${customerId}`);
    };

    return (
        <div className="space-y-4">
            <div className="flex flex-col mb-8">
                <div className="flex items-center justify-between">
                    <div className="flex flex-col gap-1">
                        <h2 className="text-2xl font-bold text-gray-900">Customers</h2>
                        <p className="text-sm text-gray-600">
                            Manage your customer relationships, track engagement, and monitor business growth
                        </p>
                    </div>
                </div>
            </div>

            <CustomersControls
                searchQuery={searchQuery}
                onSearch={handleSearch}
                onAddCustomer={() => router.replace('/customers/new')}
            />

            {isLoading && (
                <div className="bg-white rounded-lg border border-gray-200">
                    <ComponentLoading
                        message="Loading customers..."
                        className="min-h-[400px]"
                    />
                </div>
            )}

            {!isLoading && error && (
                <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <div className="text-red-600 text-center">
                        {error}
                    </div>
                </div>
            )}

            {!isLoading && !error && (
                <div className="flex flex-col">
                    <div className="bg-white overflow-x-scroll rounded-lg border border-gray-200">
                        <div className="min-w-max">
                            <div className="flex bg-gray-50 border-b border-gray-200">
                                {visibleHeaders.map((header) => (
                                    <div key={header.label} className="mx-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider flex-1">
                                        {header.label}
                                    </div>
                                ))}
                            </div>
                            <div>
                                {paginatedCustomers.map((customer) => (
                                    <div key={customer.id} className="scroll-x-auto flex border-b border-gray-200 hover:bg-gray-50 cursor-pointer" onClick={() => handleRowClick(customer.id)}>
                                        {visibleHeaders.map((header) => {
                                            let cellValue = header.key ? customer[header.key as keyof CustomerWithBasicDetails] : '';
                                            return (
                                                <div key={header.label} className="my-4 mx-4 overflow-hidden text-ellipsis text-sm text-gray-700 flex-1">
                                                    {header.label === 'Name' ? (
                                                        <div className="flex min-w-50 items-center gap-2">
                                                            {customer.type === 'BUSINESS' ? (
                                                                <Building2 className="h-4 w-4 text-blue-600" />
                                                            ) : (
                                                                <User className="h-4 w-4 text-green-600" />
                                                            )}
                                                            <span className="font-medium overflow-hidden text-ellipsis whitespace-nowrap">
                                                                {cellValue}
                                                            </span>
                                                        </div>
                                                    ) : header.label === 'Website' ? (
                                                        <span className='max-w-auto overflow-hidden text-ellipsis whitespace-nowrap'>
                                                            {customer.website ? (
                                                                <a
                                                                    href={customer.website.startsWith('http') ? customer.website : `https://${customer.website}`}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    className="text-blue-600 hover:text-blue-800 underline"
                                                                    onClick={(e) => e.stopPropagation()}
                                                                >
                                                                    {customer.website}
                                                                </a>
                                                            ) : (
                                                                <span className="text-gray-400">-</span>
                                                            )}
                                                        </span>
                                                    ) : header.label === 'Tags' ? (
                                                        <div className="flex flex-wrap gap-1">
                                                            {customer.tags && customer.tags.length > 0 ? (
                                                                customer.tags.slice(0, 2).map((tag, idx) => (
                                                                    <span
                                                                        key={idx}
                                                                        className={`px-2 py-0.5 rounded-full text-xs font-medium ${tag === 'Enterprise' ? 'bg-blue-100 text-blue-800' :
                                                                            tag === 'Individual' ? 'bg-green-100 text-green-800' :
                                                                                tag === 'B2B' ? 'bg-purple-100 text-purple-800' :
                                                                                    tag === 'B2C' ? 'bg-orange-100 text-orange-800' :
                                                                                        tag === 'High Value' ? 'bg-yellow-100 text-yellow-800' :
                                                                                            tag === 'New Lead' ? 'bg-indigo-100 text-indigo-800' :
                                                                                                'bg-gray-100 text-gray-800'
                                                                            }`}
                                                                    >
                                                                        {tag}
                                                                    </span>
                                                                ))
                                                            ) : (
                                                                <span className="text-gray-400">-</span>
                                                            )}
                                                            {customer.tags && customer.tags.length > 2 && (
                                                                <span className="text-xs text-gray-400">
                                                                    +{customer.tags.length - 2}
                                                                </span>
                                                            )}
                                                        </div>
                                                    ) : header.pill ? (
                                                        <span className={`px-3 py-1 rounded-full text-xs font-semibold ${header.label === 'Status'
                                                            ? statusColorMap[cellValue as string] || 'bg-gray-100 text-gray-800'
                                                            : header.label === 'Stage'
                                                                ? stageColorMap[cellValue as string] || 'bg-gray-100 text-gray-800'
                                                                : 'bg-gray-100 text-gray-800'
                                                            }`}>
                                                            {cellValue}
                                                        </span>
                                                    ) : (
                                                        <span className='overflow-hidden text-ellipsis whitespace-nowrap'>{cellValue}</span>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {totalPages > 1 && (
                        <div className="flex justify-end items-center p-4">
                            <PagePagination
                                currentPage={currentPage}
                                totalPages={totalPages}
                                onPageChange={setCurrentPage}
                            />
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default AllCustomersTable; 