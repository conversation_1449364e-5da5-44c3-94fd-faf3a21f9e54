'use client';

import React from 'react';
import { ArrowLeft, User, Eye, FileText, Handshake, Activity, ReceiptText, ShoppingCart } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import SecondaryNavbar, { SecondaryNavbarLayout } from '../../common/ComponentSecondaryNavbar';
import { SecondaryNavItem } from '@/types/component/common/TypeSecondaryNavbar';


interface CustomerDetailLayoutProps {
    children: React.ReactNode;
}

const ComponentCustomerDetailLayout: React.FC<CustomerDetailLayoutProps> = ({ children }) => {
    const router = useRouter();
    const params = useParams();
    const customerId = params.id as string;

    const navItems: SecondaryNavItem[] = [
        {
            id: 'overview',
            label: 'Overview',
            icon: <Eye className="h-5 w-5" />,
            href: `/customers/${customerId}`,
        },
        {
            id: 'proposals',
            label: 'Proposals',
            icon: <ReceiptText className="h-5 w-5" />,
            href: `/customers/${customerId}/proposals`,
        },
        {
            id: 'customerproducts',
            label: 'Products',
            icon: <ShoppingCart className="h-5 w-5" />,
            href: `/customers/${customerId}/products`,
        },
        // {
        //     id: 'activities',
        //     label: 'Activities',
        //     icon: <Activity className="h-5 w-5" />,
        //     href: `/customers/${customerId}/activities`,
        // },
    ];

    return (
        <>
            <div className='max-w-6xl mx-auto'>
                <div className="px-25 mt-6">
                    <button
                        onClick={() => router.push('/customers')}
                        className="flex items-center text-gray-600 hover:text-secondary transition-colors h-10 rounded-md"
                    >
                        <ArrowLeft className="h-5 w-5 mr-2" />
                        Back to Customers
                    </button>
                </div>
                <div className='-mt-4'>
                    <SecondaryNavbarLayout
                        navbar={
                            <SecondaryNavbar
                                items={navItems}
                            />
                        }
                        content={children}
                    />
                </div>
            </div>
        </>
    );
};

export default ComponentCustomerDetailLayout;
