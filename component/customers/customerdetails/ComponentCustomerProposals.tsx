'use client';

import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import {
    ReceiptText,
    User,
    Building2,
    Calendar,
    DollarSign,
    ChevronRight,
    Plus,
    Search
} from 'lucide-react';
import { useGetCustomerByIdWithProposalsQuery } from '@/lib/graphql/types/generated/hooks';
import { QuoteStatus, Currency } from '@/lib/graphql/types/generated/graphql';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import ComponentLoading from '@/component/common/ComponentLoading';
import ComponentNote from '@/component/common/ComponentNote';
import PagePagination from '@/component/pagination/ComponentPagePagination';

interface CustomerProposalsProps {
    customerId: string;
}

// Status color mapping for proposal status badges
const statusColorMap: { [key: string]: string } = {
    'CREATED': 'bg-gray-100 text-gray-800',
    'SENT': 'bg-yellow-100 text-yellow-800',
    'ACCEPTED': 'bg-green-100 text-green-800',
    'REJECTED': 'bg-red-100 text-red-800',
    'ACTIVATED': 'bg-blue-100 text-blue-800',
    'DELETED': 'bg-red-100 text-red-800',
};

// Helper function to format currency
const formatCurrency = ({ value, currency }: { value: number; currency: Currency }) => {
    return `${value.toLocaleString()} ${currency}`;
};

// Helper function to format date
const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

// Helper function to handle validity period
const formatValidityPeriod = (validTill?: string | null) => {
    if (!validTill || validTill === 'MAX') {
        return 'Unbounded';
    }
    return `Until ${formatDate(validTill)}`;
};

// Helper function to truncate quote ID for display
const truncateQuoteId = (id: string) => {
    return id.length > 8 ? `${id.substring(0, 8)}...` : id;
};

const ComponentCustomerProposals: React.FC<CustomerProposalsProps> = ({ customerId }) => {
    const router = useRouter();
    const [searchQuery, setSearchQuery] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const recordsPerPage = 7;

    // Apollo GraphQL query for customer proposals
    const { data, loading, error: queryError } = useGetCustomerByIdWithProposalsQuery({
        variables: { id: customerId },
        fetchPolicy: 'cache-and-network',
        errorPolicy: 'all'
    });

    // Extract proposals from customer data
    const proposals = data?.getCustomer?.quotes?.toSorted((a, b) => parseInt(b.version) - parseInt(a.version)) || [];

    // Filter proposals based on search query
    const filteredProposals = useMemo(() => {
        if (!searchQuery.trim()) return proposals;
        const query = searchQuery.toLowerCase().trim();
        return proposals.filter(proposal =>
            proposal.id.toLowerCase().includes(query) ||
            proposal.description?.toLowerCase().includes(query) ||
            proposal.status.toLowerCase().includes(query) ||
            proposal.version.toLowerCase().includes(query) ||
            JSON.stringify(proposal.assignments).toLowerCase().includes(query)

        ).sort(
            (a, b) => parseInt(b.version) - parseInt(a.version)
        );
    }, [searchQuery, proposals]);

    // Pagination logic
    const totalPages = Math.ceil(filteredProposals.length / recordsPerPage);
    const paginatedProposals = useMemo(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        return filteredProposals.slice(startIndex, startIndex + recordsPerPage);
    }, [filteredProposals, currentPage]);

    // Handle proposal card click - navigate to proposal editing form
    const handleProposalClick = (proposalId: string) => {
        router.push(`/catalog/proposals/new?mode=edit&id=${proposalId}&customer_id=${customerId}`);
    };

    // Handle search input change
    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
        setCurrentPage(1); // Reset to first page when searching
    };

    // Handle add new proposal
    const handleAddProposal = () => {
        router.push(`/catalog/proposals/new?customerId=${customerId}`);
    };

    // Loading state
    if (loading) {
        return (
            <div className="bg-white rounded-lg border border-gray-200">
                <ComponentLoading
                    message="Loading proposals..."
                    className="min-h-[400px]"
                />
            </div>
        );
    }

    // Error state
    if (queryError) {
        return (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
                <ComponentNote isError>
                    {getUserFriendlyErrorMessage(queryError)}
                </ComponentNote>
            </div>
        );
    }

    return (
        <div className="space-y-6 mt-20">
            {/* Header Section */}
            <div className="flex items-center justify-between">
                <div className="flex flex-col gap-1">
                    <h2 className="text-2xl font-bold text-gray-900">Customer Proposals</h2>
                    <p className="text-sm text-gray-600">
                        View and manage all proposals for this customer
                    </p>
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="text-center">
                        <div className="text-lg font-semibold text-gray-900">{proposals.length}</div>
                        <div>Total</div>
                    </div>
                    <div className="text-center">
                        <div className="text-lg font-semibold text-green-600">
                            {proposals.filter(p => p.status === QuoteStatus.Accepted).length}
                        </div>
                        <div>Accepted</div>
                    </div>
                    <div className="text-center">
                        <div className="text-lg font-semibold text-yellow-600">
                            {proposals.filter(p => p.status === QuoteStatus.Sent).length}
                        </div>
                        <div>Pending</div>
                    </div>
                </div>
            </div>

            {/* Controls Section */}
            <div className="flex items-center justify-between rounded-lg bg-white p-4 border border-gray-200">
                <div className="relative flex-1 max-w-lg">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                        type="text"
                        placeholder="Search proposals..."
                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        value={searchQuery}
                        onChange={handleSearch}
                    />
                </div>
                <button
                    onClick={handleAddProposal}
                    className="ml-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 cursor-pointer transition-colors flex items-center"
                >
                    <Plus className="h-4 w-4 mr-2" />
                    New Proposal
                </button>
            </div>

            {/* Empty State */}
            {filteredProposals.length === 0 && !loading && (
                <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <ReceiptText className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {searchQuery ? 'No proposals found' : 'No proposals yet'}
                    </h3>
                    <p className="text-gray-600 mb-4">
                        {searchQuery
                            ? 'Try adjusting your search criteria'
                            : 'Create your first proposal for this customer to get started'
                        }
                    </p>
                    {!searchQuery && (
                        <button
                            onClick={handleAddProposal}
                            className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors"
                        >
                            <Plus className="w-4 h-4" />
                            Create Proposal
                        </button>
                    )}
                </div>
            )}

            {/* Proposals Grid */}
            {paginatedProposals.length > 0 && (
                <>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {paginatedProposals.map((proposal) => (
                            <div
                                key={proposal.id}
                                className="bg-white rounded-lg border border-gray-200 cursor-pointer transition-all duration-200 hover:shadow-lg hover:border-primary"
                                onClick={() => handleProposalClick(proposal.id)}
                            >
                                <div className="p-4 space-y-4">
                                    {/* Header with Proposal Number and Status */}
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <ReceiptText className="w-5 h-5 text-blue-600" />
                                            </div>
                                            <div>
                                                <h4 className="font-semibold text-gray-900">
                                                    #{truncateQuoteId(proposal.id)}
                                                </h4>
                                                <p className="text-sm text-gray-600">Proposal</p>
                                            </div>
                                        </div>
                                        <div className='flex items-center gap-2'>
                                            <span className="text-sm px-2 py-1 text-gray-600 bg-secondary text-white px-2 py-1 rounded-full">v{proposal.version}</span>
                                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColorMap[proposal.status] || 'bg-gray-100 text-gray-800'}`}>
                                                {proposal.status}
                                            </span>
                                        </div>
                                    </div>

                                    {/* Description */}
                                    {proposal.description && (
                                        <p className="text-sm text-gray-600 line-clamp-2">
                                            {proposal.description}
                                        </p>
                                    )}

                                    {/* Assignments */}
                                    {proposal.assignments && proposal.assignments.length > 0 && (
                                        <div className="space-y-2">
                                            {proposal.assignments[0].salesExecutive && (
                                                <div className="flex items-center gap-2 text-sm">
                                                    <User className="w-4 h-4 text-gray-400" />
                                                    <span className="text-gray-600">Sales:</span>
                                                    <span className="font-medium text-gray-900">
                                                        {proposal.assignments[0].salesExecutive.name}
                                                    </span>
                                                </div>
                                            )}
                                            {proposal.assignments[0].customerSuccessManger && (
                                                <div className="flex items-center gap-2 text-sm">
                                                    <Building2 className="w-4 h-4 text-gray-400" />
                                                    <span className="text-gray-600">CSM:</span>
                                                    <span className="font-medium text-gray-900">
                                                        {proposal.assignments[0].customerSuccessManger.name}
                                                    </span>
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    {/* Dates */}
                                    <div className="space-y-1 text-sm">
                                        <div className="flex items-center gap-2">
                                            <Calendar className="w-4 h-4 text-gray-400" />
                                            <span className="text-gray-600">Created:</span>
                                            <span className="text-gray-900">
                                                {formatDate(proposal.date.validFrom)}
                                            </span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Calendar className="w-4 h-4 text-gray-400" />
                                            <span className="text-gray-600">Valid:</span>
                                            <span className="text-gray-900">
                                                {formatValidityPeriod(proposal.date.validTill)}
                                            </span>
                                        </div>
                                    </div>

                                    {/* Action indicator */}
                                    <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                                        <div className="flex items-center gap-2">
                                            <DollarSign className="w-4 h-4" />
                                            <span className="text-sm text-gray-600">Total (excl. tax):</span>
                                            <span className="font-semibold text-green-600">
                                                {formatCurrency(proposal.quoteTotalSellingPrice)}
                                            </span>
                                        </div>
                                        <div className="flex items-center gap-1 text-sm text-primary">
                                            <span>View Details</span>
                                            <ChevronRight className="w-4 h-4" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className="flex justify-center">
                            <PagePagination
                                currentPage={currentPage}
                                totalPages={totalPages}
                                onPageChange={setCurrentPage}
                            />
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

export default ComponentCustomerProposals;
