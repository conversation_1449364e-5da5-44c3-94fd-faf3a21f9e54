import { gql } from "@apollo/client";

export const GET_ALL_CUSTOMERS = gql`
  query GetAllCustomers($status: CustomerStatus, $stage: CustomerStage, $customerType: CustomerType) {
      getCustomers(
        filters: {
          status: $status,
          stage: $stage,
          customerType: $customerType
        }
      ) {
        id
        company {
          id
        }
        status
        stage
        type
        assignments {
          accountManager {
            id
            name
            email
          }
          supportRepresentative {
            id
            name
            email
          }
        }
        ...on CustomerIndividual {
          basicDetails {
              contactDetails {
                  contactType
                  name
                  title
                  email
                  phoneNo
              }
              individualWebsite
              plainTags
              referralSource
        }
      }
      ... on CustomerBusiness {
          basicDetails {
              legalName
              website
              size
              industry
              referralSource
              plainTags
              contactDetails {
                  contactType
                  name
                  title
                  email
                  phoneNo
              }
          }
      }
    }
  }
`;

export const GET_CUSTOMER_BY_ID = gql`
  query GetCustomerById($id: ID!) {
    getCustomer(id: $id) {
      id
      status
      stage
      type
      company {
        id
      }
      ... on CustomerIndividual {
        basicDetails {
          contactDetails {
            contactType
            name
            title
            email
            phoneNo
          }
          individualWebsite
          plainTags
          referralSource
        }
        notes {
          id
          content
          tags
        }
        assignments {
          accountManager {
            id
            name
            email
          }
          supportRepresentative {
            id
            name
            email
          }
        }
        customTags {
          id
          key
          label
          value
          type
          description
        }
      }
      ... on CustomerBusiness { 
        basicDetails {
          legalName
          website
          size
          industry
          referralSource
          plainTags
          contactDetails {
            contactType
            name
            title
            email
            phoneNo
          }
        }
        notes {
          id
          content
          tags
        }
        assignments {
          accountManager {
            id
            name
            email
          }
          supportRepresentative {
            id
            name
            email
          }
        }
        customTags {
          id
          key
          label
          value
          type
          description
        }
      }
   }
}
`;


export const GET_CUSTOMER_BY_ID_WITH_PROPOSALS = gql`
  query GetCustomerByIdWithProposals($id: ID!) {
    getCustomer(id: $id) {
      id
      quotes {
        id
        status
        description
        version
        assignments {
          salesExecutive {
            id
            name
          }
          customerSuccessManger {
            id
            name
          }
        }
        date {
          validFrom
          validTill
        }
        currency
        quoteTotalSellingPrice {
          value
          currency
        }
        quoteTotalTaxAmount {
          value
          currency
        }
        quoteTotalDiscountAmount {
          value
          currency
        }
      }
    }
  }
`;