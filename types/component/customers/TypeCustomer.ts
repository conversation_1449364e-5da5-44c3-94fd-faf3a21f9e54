import { CustomerStage, CustomerStatus, CustomerType, GetCustomerByIdQuery } from '@/lib/graphql/types/generated/graphql';

export interface CustomerWithBasicDetails {
    id: string;
    name: string;
    email: string;
    status: CustomerStatus;
    phone: string;
    type: CustomerType;
    stage: CustomerStage;
    website?: string;
    tags?: string[];
    accountManager?: string
}

export interface CustomersControlsProps {
    searchQuery: string;
    onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onAddCustomer: () => void;
}